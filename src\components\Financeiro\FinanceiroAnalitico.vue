<template>
  <div class="financeiro-analitico">
    <!-- Filtros e Busca -->
    <div class="row mb-4 mx-0">
      <div class="col-12 px-0">
        <div class="card card-full-width">
          <div class="card-body">
            <div class="row g-3">
              <!-- Primeira linha: Status e Datas -->
              <div class="col-md-6">
                <label class="form-label">Status</label>
                <div class="status-toggles w-100">
                  <button
                    v-for="status in statusOptions"
                    :key="status.value"
                    class="btn status-toggle flex-fill mb-0"
                    :class="getStatusToggleClass(status.value)"
                    @click="toggleStatus(status.value)"
                    :title="status.label"
                  >
                    <i :class="status.icon" class="me-1"></i>
                    {{ status.label }}
                  </button>
                </div>
              </div>
              <div class="col-md-6">
                <div class="row g-2">
                  <div class="col-6">
                    <label class="form-label">Data Início</label>
                    <input type="date" class="form-control" v-model="filters.data_inicio" @change="applyFilters">
                  </div>
                  <div class="col-6">
                    <label class="form-label">Data Fim</label>
                    <input type="date" class="form-control" v-model="filters.data_fim" @change="applyFilters">
                  </div>
                </div>
              </div>
            </div>

            <div class="row g-3 mt-2">
              <!-- Segunda linha: Descrição e Paciente -->
              <div class="col-md-6">
                <label class="form-label">Descrição da Fatura</label>
                <input
                  type="text"
                  class="form-control"
                  v-model="filters.descricao"
                  @input="applyFilters"
                  placeholder="Buscar por descrição..."
                >
              </div>
              <div class="col-md-6">
                <label class="form-label">Paciente</label>
                <select class="form-select" v-model="filters.paciente_id" @change="applyFilters">
                  <option value="">Todos os pacientes</option>
                  <option v-for="paciente in pacientes" :key="paciente.id" :value="paciente.id">
                    {{ paciente.nome }}
                  </option>
                </select>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Estatísticas Rápidas -->
    <div class="row mb-4 mx-3" v-if="estatisticas">
      <div class="col-md-3">
        <div class="stats-card">
          <div class="stats-title">
            <i class="fas fa-check-circle me-1"></i>
            Recebido ({{ estatisticas.quantidade_paga || 0 }})
          </div>
          <div class="stats-value text-success">{{ formatCurrency(estatisticas.total_pago) }}</div>
        </div>
      </div>
      <div class="col-md-3">
        <div class="stats-card">
          <div class="stats-title">
            <i class="fas fa-clock me-1"></i>
            Pendente ({{ estatisticas.quantidade_pendente || 0 }})
          </div>
          <div class="stats-value text-warning">{{ formatCurrency(estatisticas.total_pendente) }}</div>
        </div>
      </div>
      <div class="col-md-3">
        <div class="stats-card">
          <div class="stats-title">
            <i class="fas fa-exclamation-triangle me-1"></i>
            Vencido ({{ estatisticas.quantidade_vencida || 0 }})
          </div>
          <div class="stats-value text-danger">{{ formatCurrency(estatisticas.total_vencido) }}</div>
        </div>
      </div>
      <div class="col-md-3">
        <div class="stats-card">
          <div class="stats-title">
            <i class="fas fa-calculator me-1"></i>
            Total Geral
          </div>
          <div class="stats-value text-info">{{ formatCurrency(estatisticas.total_geral) }}</div>
        </div>
      </div>
    </div>

    <!-- Tabela de Faturas -->
    <div class="row mx-0">
      <div class="col-12 px-0">
        <div class="card card-full-width">
          <div class="card-header pb-0">
            <h6 class="mb-0">Faturas</h6>
          </div>
          <div class="card-body px-0 pt-0 pb-2">
            <div class="table-responsive p-0" style="overflow: visible;">
              <table class="table align-items-center mb-0">
                <thead>
                  <tr>
                    <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">
                      Paciente
                    </th>
                    <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7 ps-2">
                      Descrição
                    </th>
                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">
                      Valor
                    </th>
                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">
                      Vencimento
                    </th>
                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">
                      Status
                    </th>
                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">
                      Ações
                    </th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-if="loading">
                    <td colspan="6" class="text-center py-4">
                      <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Carregando...</span>
                      </div>
                    </td>
                  </tr>
                  <tr v-else-if="faturas.length === 0">
                    <td colspan="6" class="text-center py-4 text-muted">
                      Nenhuma fatura encontrada
                    </td>
                  </tr>
                  <tr v-else v-for="fatura in faturas" :key="fatura.id">
                    <td>
                      <div class="d-flex px-2 py-1">
                        <div class="d-flex flex-column justify-content-center">
                          <h6 class="mb-0 text-sm">{{ fatura.paciente?.nome || 'Paciente não informado' }}</h6>
                          <p class="text-xs text-secondary mb-0">
                            ID: {{ fatura.paciente?.id || '-' }}
                          </p>
                        </div>
                      </div>
                    </td>
                    <td>
                      <p class="text-xs font-weight-bold mb-0">{{ fatura.descricao }}</p>
                      <p class="text-xs text-secondary mb-0" v-if="fatura.observacoes">
                        {{ fatura.observacoes }}
                      </p>
                    </td>
                    <td class="align-middle text-center text-sm">
                      <span class="font-weight-bold">{{ formatCurrency(fatura.valor_final) }}</span>
                      <div v-if="fatura.parcelas_total > 1" class="text-xs text-secondary">
                        {{ fatura.parcela_numero }}/{{ fatura.parcelas_total }}
                      </div>
                    </td>
                    <td class="align-middle text-center">
                      <span class="text-secondary text-xs font-weight-bold">
                        {{ formatDate(fatura.data_vencimento) }}
                      </span>
                    </td>
                    <td class="align-middle text-center text-sm">
                      <span class="badge badge-sm" :class="getStatusBadgeClass(fatura.status)">
                        {{ getStatusText(fatura.status) }}
                      </span>
                    </td>
                    <td class="align-middle text-center">
                      <div class="actions-dropdown-container" style="position: relative;">
                        <button
                          class="btn btn-link text-secondary mb-0 actions-btn"
                          type="button"
                          @click.stop="toggleDropdown(fatura.id)"
                        >
                          <i class="fas fa-ellipsis-v"></i>
                        </button>
                        <div
                          v-if="activeDropdown === fatura.id"
                          class="custom-dropdown-menu"
                          @click.stop
                        >
                          <div class="custom-dropdown-item" @click="$emit('edit', fatura)">
                            <i class="fas fa-edit me-2"></i>
                            Editar
                          </div>
                          <div v-if="fatura.status === 'pendente'" class="custom-dropdown-item" @click="markAsPaid(fatura)">
                            <i class="fas fa-check me-2"></i>
                            Marcar como Pago
                          </div>
                          <div v-if="fatura.status === 'pago'" class="custom-dropdown-item" @click="generateReceipt(fatura)">
                            <i class="fas fa-file-pdf me-2"></i>
                            Gerar Recibo
                          </div>
                          <div class="custom-dropdown-item" @click="duplicateFatura(fatura)">
                            <i class="fas fa-copy me-2"></i>
                            Duplicar Fatura
                          </div>
                          <div v-if="fatura.status === 'pendente' || fatura.status === 'vencido'" class="custom-dropdown-item" @click="sendReminder(fatura)">
                            <i class="fas fa-envelope me-2"></i>
                            Enviar Lembrete
                          </div>
                          <div class="custom-dropdown-divider"></div>
                          <div class="custom-dropdown-item text-danger" @click="confirmDelete(fatura)">
                            <i class="fas fa-trash me-2"></i>
                            Cancelar Fatura
                          </div>
                        </div>
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { financeiroService } from '@/services/financeiroService';
import { searchPacientes } from '@/services/pacientesService';

export default {
  name: 'FinanceiroAnalitico',
  props: {
    faturas: {
      type: Array,
      default: () => []
    },
    loading: {
      type: Boolean,
      default: false
    },
    estatisticas: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      pacientes: [],
      activeDropdown: null,
      filters: {
        paciente_id: '',
        descricao: '',
        statusList: [], // Array de status ativos
        data_inicio: '',
        data_fim: ''
      },
      statusOptions: [
        { value: 'pendente', label: 'Pendente', icon: 'fas fa-clock' },
        { value: 'pago', label: 'Pago', icon: 'fas fa-check-circle' },
        { value: 'vencido', label: 'Vencido', icon: 'fas fa-exclamation-triangle' },
        { value: 'cancelado', label: 'Cancelado', icon: 'fas fa-times-circle' }
      ]
    };
  },
  methods: {
    formatCurrency: financeiroService.formatCurrency,
    formatDate: financeiroService.formatDate,
    getStatusBadgeClass: financeiroService.getStatusBadgeClass,
    getStatusText: financeiroService.getStatusText,

    applyFilters() {
      // Converter statusList para o formato esperado pelo backend
      const filtersToSend = {
        ...this.filters,
        status: this.filters.statusList.length > 0 ? this.filters.statusList : ''
      };
      this.$emit('refresh', filtersToSend);
    },

    toggleStatus(status) {
      const index = this.filters.statusList.indexOf(status);
      if (index > -1) {
        // Remove o status se já estiver selecionado
        this.filters.statusList.splice(index, 1);
      } else {
        // Adiciona o status se não estiver selecionado
        this.filters.statusList.push(status);
      }
      this.applyFilters();
    },

    getStatusToggleClass(status) {
      const isActive = this.filters.statusList.includes(status);
      const baseClass = 'btn-outline-';
      const activeClass = 'btn-';

      switch (status) {
        case 'pendente':
          return isActive ? activeClass + 'warning' : baseClass + 'warning';
        case 'pago':
          return isActive ? activeClass + 'success' : baseClass + 'success';
        case 'vencido':
          return isActive ? activeClass + 'danger' : baseClass + 'danger';
        case 'cancelado':
          return isActive ? activeClass + 'secondary' : baseClass + 'secondary';
        default:
          return baseClass + 'primary';
      }
    },

    markAsPaid(fatura) {
      const paymentData = {
        data_pagamento: new Date().toISOString().split('T')[0],
        meio_pagamento: 'Não especificado'
      };
      this.$emit('mark-paid', fatura.id, paymentData);
    },

    generateReceipt(fatura) {
      // Emitir evento para gerar recibo
      this.$emit('generate-receipt', fatura);
    },

    duplicateFatura(fatura) {
      // Emitir evento para duplicar fatura
      this.$emit('duplicate', fatura);
    },

    sendReminder(fatura) {
      // Emitir evento para enviar lembrete
      this.$emit('send-reminder', fatura);
    },

    confirmDelete(fatura) {
      if (confirm(`Tem certeza que deseja cancelar a fatura "${fatura.descricao}"?`)) {
        this.$emit('delete', fatura.id);
      }
    },

    toggleDropdown(faturaId) {
      this.activeDropdown = this.activeDropdown === faturaId ? null : faturaId;
    },

    closeDropdown() {
      this.activeDropdown = null;
    },

    async loadPacientes() {
      try {
        const response = await searchPacientes();
        this.pacientes = response || [];
      } catch (error) {
        console.error('Erro ao carregar pacientes:', error);
        this.pacientes = [];
      }
    }
  },

  mounted() {
    this.loadPacientes();
    // Fechar dropdown ao clicar fora
    document.addEventListener('click', this.closeDropdown);
  },

  beforeUnmount() {
    document.removeEventListener('click', this.closeDropdown);
  }
};
</script>

<style scoped>
.financeiro-analitico .card {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  overflow: visible;
}

.financeiro-analitico .card-full-width {
  border-radius: 0;
}

.badge-warning {
  background-color: #fb6340;
}

/* Estilos para os cards de estatísticas */
.stats-card {
  background-color: #fff;
  border-radius: 8px;
  padding: 0.8rem;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.stats-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
}

.stats-title {
  font-size: 0.75rem;
  font-weight: 600;
  color: #344767;
  text-transform: uppercase;
  margin-bottom: 0.4rem;
  letter-spacing: 0.5px;
}

.stats-value {
  font-size: 1.1rem;
  font-weight: 700;
  margin-bottom: 0.25rem;
}

.badge-success {
  background-color: #2dce89;
}

.badge-danger {
  background-color: #f5365c;
}

.badge-secondary {
  background-color: #8898aa;
}

/* Estilos para os toggles de status */
.status-toggles {
  display: flex;
  gap: 0;
  border-radius: 0.375rem;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  width: 100%;
}

.status-toggle {
  border-radius: 0 !important;
  border-right: none !important;
  font-size: 0.75rem;
  padding: 0.4rem 0.5rem;
  font-weight: 600;
  transition: all 0.2s ease;
  white-space: nowrap;
  flex: 1;
  text-align: center;
}

.status-toggle:last-child {
  border-right: 1px solid !important;
}

.status-toggle:first-child {
  border-top-left-radius: 0.375rem !important;
  border-bottom-left-radius: 0.375rem !important;
}

.status-toggle:last-child {
  border-top-right-radius: 0.375rem !important;
  border-bottom-right-radius: 0.375rem !important;
}

.status-toggle:hover {
  transform: translateY(-1px);
  z-index: 2;
  position: relative;
}

/* Dropdown de ações */
.dropdown-menu {
  z-index: 1050;
  min-width: 160px;
}

.dropdown-toggle::after {
  display: none !important;
}

/* Menu de ações customizado */
.actions-dropdown-container {
  position: relative;
}

.actions-btn {
  padding: 0.25rem 0.5rem;
  border: none;
  background: transparent;
  cursor: pointer;
  transition: all 0.2s ease;
}

.actions-btn:hover {
  background-color: rgba(0, 0, 0, 0.05);
  border-radius: 4px;
}

.custom-dropdown-menu {
  position: absolute;
  top: 100%;
  right: 0;
  z-index: 1050;
  min-width: 180px;
  background: white;
  border: 1px solid rgba(0, 0, 0, 0.15);
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  padding: 0.5rem 0;
  margin-top: 0.25rem;
  animation: dropdownFadeIn 0.15s ease-out;
}

@keyframes dropdownFadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.custom-dropdown-item {
  padding: 0.5rem 1rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  font-size: 0.875rem;
  color: #344767;
  transition: background-color 0.2s ease;
  white-space: nowrap;
}

.custom-dropdown-item:hover {
  background-color: #f8f9fa;
}

.custom-dropdown-item.text-danger {
  color: #dc3545;
}

.custom-dropdown-item.text-danger:hover {
  background-color: #f8d7da;
}

.custom-dropdown-divider {
  height: 1px;
  background-color: #e9ecef;
  margin: 0.5rem 0;
}
</style>
